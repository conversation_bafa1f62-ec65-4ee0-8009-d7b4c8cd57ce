import 'dart:async';
import 'dart:developer' as dev;

import 'package:flutter_modular/flutter_modular.dart';
import 'package:koc_app/src/base/cubit/base_cubit.dart';
import 'package:koc_app/src/modules/account/data/repository/account_repository.dart';
import 'package:koc_app/src/modules/campaign/cubit/campaign_home_state.dart';
import 'package:koc_app/src/modules/campaign/data/models/campaign.dart';
import 'package:koc_app/src/modules/campaign/presentation/mixin/campaign_detail_mixin.dart';
import 'package:koc_app/src/modules/survey/data/models/survey_info.dart';
import 'package:koc_app/src/modules/survey/data/repository/survey_repository.dart';
import 'package:koc_app/src/shared/services/api_service.dart';
import 'package:koc_app/src/shared/utils/handle_error.dart';

import '../data/repository/campaign_repository.dart';

class CampaignHomeCubit extends BaseCubit<CampaignHomeState> with CampaignDetailMixin {
  final CampaignRepository campaignRepository;
  final SurveyRepository surveyRepository;
  final AccountRepository accountRepository;

  final int pageSize = 10;
  final int currentPage = 1;
  int _currentRequestId = 0;

  CampaignHomeCubit(this.campaignRepository, this.surveyRepository, this.accountRepository)
      : super(CampaignHomeState());

  Future<void> fetchHomeCampaigns() async {
    emit(state.copyWith(isLoading: true));
    try {
      final siteId = (await commonCubit.sharedPreferencesService.getCurrentSiteId())!;
      final currency = await commonCubit.sharedPreferencesService.getPublisherCurrency();
      List<dynamic> result = await Future.wait([
        surveyRepository.getPassionateInfo(),
        _findCampaignCountSummary(siteId),
        _findCampaignFeatureSummary(siteId),
        _loadAvailableCampaigns(CampaignFilter()),
        _loadWaitingCampaigns(CampaignFilter()),
        _loadAffiliatedCampaigns(CampaignFilter()),
        _loadPausedCampaigns(CampaignFilter()),
      ]);

      final passionateInfo = result[0] as PassionateInfo;
      final campaignSummariesCount = result[1] as CampaignCountSummary;
      final recommendCampaigns = result[2] as List<DefaultCampaignSummary>;
      final availableCampaigns = result[3] as List<DefaultCampaignSummary>;
      final waitingCampaigns = result[4] as List<DefaultCampaignSummary>;
      final affiliatedCampaigns = result[5] as List<DefaultCampaignSummary>;
      final pausedCampaigns = result[6] as List<DefaultCampaignSummary>;

      emit(state.copyWith(
        isLoading: false,
        isSiteSwitching: false,
        campaignSummariesCount: campaignSummariesCount,
        availableCampaigns: availableCampaigns,
        recommendCampaigns: recommendCampaigns,
        waitingCampaigns: waitingCampaigns,
        affiliatedCampaigns: affiliatedCampaigns,
        pausedCampaigns: pausedCampaigns,
        passionateInfo: passionateInfo,
        currency: currency ?? "",
        selectedSiteId: siteId,
      ));
    } catch (e) {
      handleError(e, (message) {
        emit(state.copyWith(
          isLoading: false,
          isSiteSwitching: false,
          errorMessage: message,
        ));
      });
    }
  }

  /// Method to be called when site switching starts
  /// This sets the isSiteSwitching flag to suppress local loading indicators
  void startSiteSwitching() {
    emit(state.copyWith(isSiteSwitching: true));
  }

  /// Method to be called when site switching ends
  /// This clears the isSiteSwitching flag to restore normal loading behavior
  void endSiteSwitching() {
    emit(state.copyWith(isSiteSwitching: false));
  }

  Future<CampaignCountSummary> _findCampaignCountSummary(int siteId) async {
    try {
      CampaignCountSummary campaignCountSummary = await campaignRepository.findCampaignCountSummary(siteId);
      return campaignCountSummary;
    } catch (e) {
      handleError(e, (message) => dev.log(message));
      return const CampaignCountSummary();
    }
  }

  Future<int> refreshCampaignCountSummary() async {
    try {
      commonCubit.loadingVisibility(true);
      final siteId = (await commonCubit.sharedPreferencesService.getCurrentSiteId())!;
      final oldSummary = state.campaignSummariesCount;

      CampaignCountSummary campaignCountSummary = await campaignRepository.findCampaignCountSummary(siteId);

      int tabWithIncreasedCount = -1;
      final tabsToReload = <int>{};

      if (campaignCountSummary.waitingCount != oldSummary.waitingCount) {
        tabsToReload.add(CampaignTabIndex.WAITING.index);
        if (campaignCountSummary.waitingCount > oldSummary.waitingCount) {
          tabWithIncreasedCount = CampaignTabIndex.WAITING.index;
        }
      }

      if (campaignCountSummary.affiliatedCount != oldSummary.affiliatedCount) {
        tabsToReload.add(CampaignTabIndex.AFFILIATED.index);
        if (campaignCountSummary.affiliatedCount > oldSummary.affiliatedCount && tabWithIncreasedCount < 0) {
          tabWithIncreasedCount = CampaignTabIndex.AFFILIATED.index;
        }
      }

      if (campaignCountSummary.pausedCount != oldSummary.pausedCount) {
        tabsToReload.add(CampaignTabIndex.PAUSED.index);
      }

      if (campaignCountSummary.availableCount != oldSummary.availableCount) {
        tabsToReload.add(CampaignTabIndex.AVAILABLE.index);
      }

      emit(state.copyWith(campaignSummariesCount: campaignCountSummary));

      if (tabsToReload.contains(state.tabIndex)) {
        await refreshCurrentTabData();
      }

      return tabWithIncreasedCount;
    } catch (e) {
      handleError(e, (message) => dev.log(message));
      emit(state.copyWith(campaignSummariesCount: const CampaignCountSummary()));
      return -1;
    } finally {
      commonCubit.loadingVisibility(false);
    }
  }

  Future<List<DefaultCampaignSummary>> _findCampaignFeatureSummary(int siteId) async {
    try {
      final result = await campaignRepository.findCampaignFeatureSummary(siteId);
      return _parseCampaignResult(result);
    } catch (e) {
      handleError(e, (message) => dev.log(message));
      return [];
    }
  }

  List<DefaultCampaignSummary> _parseCampaignResult(dynamic result) {
    if (result == null) return [];
    return (result as List<dynamic>)
        .map((item) => DefaultCampaignSummary.fromJson(item as Map<String, dynamic>))
        .toList();
  }

  Future<List<DefaultCampaignSummary>> _loadCampaigns(int tabIndex, CampaignFilter filter) async {
    try {
      switch (CampaignTabIndex.values[tabIndex]) {
        case CampaignTabIndex.AVAILABLE:
          return await _loadAvailableCampaigns(filter);
        case CampaignTabIndex.WAITING:
          return await _loadWaitingCampaigns(filter);
        case CampaignTabIndex.AFFILIATED:
          return await _loadAffiliatedCampaigns(filter);
        case CampaignTabIndex.PAUSED:
          return await _loadPausedCampaigns(filter);
      }
    } catch (e) {
      handleError(e, (message) => dev.log(message));
      return [];
    }
  }

  Future<List<DefaultCampaignSummary>> _loadCampaignsByType(
      Future<dynamic> Function(int, CampaignFilter) fetchFunction, CampaignFilter filter) async {
    try {
      final siteId = (await commonCubit.sharedPreferencesService.getCurrentSiteId())!;
      final result = await fetchFunction(siteId, filter);
      return _parseCampaignResult(result);
    } catch (e) {
      handleError(e, (message) => dev.log(message));
      return [];
    }
  }

  /// Refreshes the data for the current tab
  Future<void> refreshCurrentTabData() async {
    emit(state.copyWith(isLoading: true));

    final int requestId = ++_currentRequestId;
    final int currentTabIndex = state.tabIndex;

    try {
      final filter = CampaignFilter(page: 1, limit: pageSize);
      final campaigns = await _loadCampaigns(currentTabIndex, filter);

      if (requestId != _currentRequestId) {
        return;
      }

      emit(state.copyWith(
        isLoading: false,
        availableCampaigns: currentTabIndex == CampaignTabIndex.AVAILABLE.index ? campaigns : state.availableCampaigns,
        waitingCampaigns: currentTabIndex == CampaignTabIndex.WAITING.index ? campaigns : state.waitingCampaigns,
        affiliatedCampaigns:
            currentTabIndex == CampaignTabIndex.AFFILIATED.index ? campaigns : state.affiliatedCampaigns,
        pausedCampaigns: currentTabIndex == CampaignTabIndex.PAUSED.index ? campaigns : state.pausedCampaigns,
      ));
    } catch (e) {
      if (requestId != _currentRequestId) return;

      handleError(e, (message) {
        emit(state.copyWith(
          isLoading: false,
          errorMessage: message,
        ));
      });
    }
  }

  Future<List<DefaultCampaignSummary>> _loadAvailableCampaigns(CampaignFilter filter) async {
    return _loadCampaignsByType(campaignRepository.findAvailableCampaigns, filter);
  }

  Future<List<DefaultCampaignSummary>> _loadWaitingCampaigns(CampaignFilter filter) async {
    return _loadCampaignsByType(campaignRepository.findWaitingCampaigns, filter);
  }

  Future<List<DefaultCampaignSummary>> _loadAffiliatedCampaigns(CampaignFilter filter) async {
    return _loadCampaignsByType(campaignRepository.findAffiliatedCampaigns, filter);
  }

  Future<List<DefaultCampaignSummary>> _loadPausedCampaigns(CampaignFilter filter) async {
    return _loadCampaignsByType(campaignRepository.findPausedCampaigns, filter);
  }

  Future<void> changeTab(int index) async {
    if (state.tabIndex == index) {
      return;
    }

    emit(state.copyWith(
      tabIndex: index,
      currentPage: 1,
      isLoadingMore: false,
    ));

    Future.microtask(() {
      _reloadCampaignForCurrentTab();
    });
  }

  Future<void> _reloadCampaignForCurrentTab() async {
    await refreshCurrentTabData();
  }

  void setNotificationMessage(String message) {
    emit(state.copyWith(notificationMessage: message));
  }

  void clearNotificationMessage() {
    emit(state.copyWith(notificationMessage: ""));
  }

  Future<void> savePassionateInfo() async {
    try {
      final siteId = (await commonCubit.sharedPreferencesService.getCurrentSiteId())!;
      List<int> categoryIds = state.passionateInfo.selectedPassionateItems.map((e) => e.id).toList();
      await accountRepository.upsertInterestedFields(siteId, categoryIds);

      await _clearInterestedFieldsRelatedCaches(siteId);
    } catch (e) {
      handleError(e, (message) {
        emit(state.copyWith(
          isLoading: false,
          errorMessage: message,
        ));
        dev.log(message);
      });
    }
  }

  Future<void> getPassionateInfo() async {
    try {
      final siteId = (await commonCubit.sharedPreferencesService.getCurrentSiteId())!;
      final selectedPassionateItems = await accountRepository.getInterestedFields(siteId);
      emit(state.copyWith(passionateInfo: PassionateInfo(selectedPassionateItems: selectedPassionateItems)));
    } catch (e) {
      handleError(e, (message) {
        emit(state.copyWith(
          isLoading: false,
          errorMessage: message,
        ));
        dev.log(message);
      });
    }
  }

  void selectPassionateInfo(PassionateItem passionateItem) {
    List<PassionateItem> selectedPassionateItems = List.from(state.passionateInfo.selectedPassionateItems);

    if (state.passionateInfo.selectedPassionateItems.contains(passionateItem)) {
      selectedPassionateItems.remove(passionateItem);
    } else {
      selectedPassionateItems.add(passionateItem);
    }

    emit(state.copyWith(passionateInfo: PassionateInfo(selectedPassionateItems: selectedPassionateItems)));
  }

  void clearPassionateInfo() {
    emit(state.copyWith(passionateInfo: const PassionateInfo()));
  }

  List<DefaultCampaignSummary> updateCampaignsForTab(int tabIndex, List<DefaultCampaignSummary> newMoreCampaigns) {
    switch (CampaignTabIndex.values[tabIndex]) {
      case CampaignTabIndex.AVAILABLE:
        return List.from(state.availableCampaigns)..addAll(newMoreCampaigns);
      case CampaignTabIndex.WAITING:
        return List.from(state.waitingCampaigns)..addAll(newMoreCampaigns);
      case CampaignTabIndex.AFFILIATED:
        return List.from(state.affiliatedCampaigns)..addAll(newMoreCampaigns);
      case CampaignTabIndex.PAUSED:
        return List.from(state.pausedCampaigns)..addAll(newMoreCampaigns);
    }
  }

  Future<void> fetchMoreCampaigns(int tabIndex) async {
    if (state.isLoadingMore || !isHasMore(tabIndex)) return;

    if (tabIndex != state.tabIndex) {
      return;
    }

    emit(state.copyWith(isLoadingMore: true));

    final int requestId = ++_currentRequestId;

    try {
      await Future.delayed(const Duration(milliseconds: 100));

      final filter = CampaignFilter(page: state.currentPage + 1, limit: pageSize);
      final newMoreCampaigns = await _loadCampaigns(tabIndex, filter);

      if (requestId != _currentRequestId || tabIndex != state.tabIndex) {
        return;
      }

      final updatedCampaigns = updateCampaignsForTab(tabIndex, newMoreCampaigns);

      final currentTabIndex = state.tabIndex;
      emit(state.copyWith(
        availableCampaigns:
            currentTabIndex == CampaignTabIndex.AVAILABLE.index ? updatedCampaigns : state.availableCampaigns,
        waitingCampaigns: currentTabIndex == CampaignTabIndex.WAITING.index ? updatedCampaigns : state.waitingCampaigns,
        affiliatedCampaigns:
            currentTabIndex == CampaignTabIndex.AFFILIATED.index ? updatedCampaigns : state.affiliatedCampaigns,
        pausedCampaigns: currentTabIndex == CampaignTabIndex.PAUSED.index ? updatedCampaigns : state.pausedCampaigns,
        currentPage: state.currentPage + 1,
        isLoadingMore: false,
        errorMessage: "",
      ));
    } catch (e) {
      if (requestId != _currentRequestId || tabIndex != state.tabIndex) return;

      handleError(e, (message) {
        emit(state.copyWith(isLoadingMore: false, errorMessage: message));
      });
    }
  }

  int getTotalPages(int totalCount) {
    if (totalCount <= 0) return 0;
    return (totalCount / pageSize).ceil();
  }

  bool isHasMore(int tabIndex) {
    int totalPages = 0;

    switch (CampaignTabIndex.values[tabIndex]) {
      case CampaignTabIndex.AVAILABLE:
        totalPages = getTotalPages(state.campaignSummariesCount.availableCount);
        break;
      case CampaignTabIndex.WAITING:
        totalPages = getTotalPages(state.campaignSummariesCount.waitingCount);
        break;
      case CampaignTabIndex.AFFILIATED:
        totalPages = getTotalPages(state.campaignSummariesCount.affiliatedCount);
        break;
      case CampaignTabIndex.PAUSED:
        totalPages = getTotalPages(state.campaignSummariesCount.pausedCount);
        break;
    }
    return state.currentPage < totalPages;
  }

  /// Comprehensive refresh method for pull-to-refresh functionality
  /// This method refreshes both campaign counts and current tab data
  /// while bypassing cache to ensure fresh data from the server
  /// Uses isPullToRefresh flag to prevent multiple loading indicators
  Future<void> pullToRefresh() async {
    try {
      emit(state.copyWith(isPullToRefresh: true));

      final apiService = Modular.get<ApiService>();
      final siteId = await commonCubit.sharedPreferencesService.getCurrentSiteId();

      if (siteId != null) {
        await apiService.clearCacheForEndpoint('/v3/publishers/me/sites/$siteId/campaigns/count-summary');

        switch (CampaignTabIndex.values[state.tabIndex]) {
          case CampaignTabIndex.AVAILABLE:
            await apiService.clearCacheForEndpoint('/v3/publishers/me/sites/$siteId/campaigns/available');
            break;
          case CampaignTabIndex.WAITING:
            await apiService.clearCacheForEndpoint('/v3/publishers/me/sites/$siteId/campaigns/waiting');
            break;
          case CampaignTabIndex.AFFILIATED:
            await apiService.clearCacheForEndpoint('/v3/publishers/me/sites/$siteId/campaigns/affiliated');
            break;
          case CampaignTabIndex.PAUSED:
            await apiService.clearCacheForEndpoint('/v3/publishers/me/sites/$siteId/campaigns/paused');
            break;
        }
      }

      await Future.wait([
        _refreshCampaignCountSummaryForPullToRefresh(),
        _refreshCurrentTabDataForPullToRefresh(),
      ]);
    } catch (e) {
      handleError(e, (message) => dev.log("Error during pull-to-refresh: $message"));
    } finally {
      emit(state.copyWith(isPullToRefresh: false));
    }
  }

  /// Specialized refresh method for campaign count summary during pull-to-refresh
  /// This method doesn't trigger loading states to avoid multiple loading indicators
  Future<int> _refreshCampaignCountSummaryForPullToRefresh() async {
    try {
      final siteId = (await commonCubit.sharedPreferencesService.getCurrentSiteId())!;
      final oldSummary = state.campaignSummariesCount;

      CampaignCountSummary campaignCountSummary = await campaignRepository.findCampaignCountSummary(siteId);

      int tabWithIncreasedCount = -1;
      final tabsToReload = <int>{};

      if (campaignCountSummary.waitingCount != oldSummary.waitingCount) {
        tabsToReload.add(CampaignTabIndex.WAITING.index);
        if (campaignCountSummary.waitingCount > oldSummary.waitingCount) {
          tabWithIncreasedCount = CampaignTabIndex.WAITING.index;
        }
      }

      if (campaignCountSummary.affiliatedCount != oldSummary.affiliatedCount) {
        tabsToReload.add(CampaignTabIndex.AFFILIATED.index);
        if (campaignCountSummary.affiliatedCount > oldSummary.affiliatedCount && tabWithIncreasedCount < 0) {
          tabWithIncreasedCount = CampaignTabIndex.AFFILIATED.index;
        }
      }

      if (campaignCountSummary.pausedCount != oldSummary.pausedCount) {
        tabsToReload.add(CampaignTabIndex.PAUSED.index);
      }

      if (campaignCountSummary.availableCount != oldSummary.availableCount) {
        tabsToReload.add(CampaignTabIndex.AVAILABLE.index);
      }

      emit(state.copyWith(campaignSummariesCount: campaignCountSummary));
      return tabWithIncreasedCount;
    } catch (e) {
      handleError(e, (message) => dev.log(message));
      return -1;
    }
  }

  /// Specialized refresh method for current tab data during pull-to-refresh
  /// This method doesn't trigger loading states to avoid multiple loading indicators
  Future<void> _refreshCurrentTabDataForPullToRefresh() async {
    final int requestId = ++_currentRequestId;
    final int currentTabIndex = state.tabIndex;

    try {
      final filter = CampaignFilter(page: 1, limit: pageSize);
      final campaigns = await _loadCampaigns(currentTabIndex, filter);

      if (requestId != _currentRequestId) {
        return;
      }

      emit(state.copyWith(
        availableCampaigns: currentTabIndex == CampaignTabIndex.AVAILABLE.index ? campaigns : state.availableCampaigns,
        waitingCampaigns: currentTabIndex == CampaignTabIndex.WAITING.index ? campaigns : state.waitingCampaigns,
        affiliatedCampaigns:
            currentTabIndex == CampaignTabIndex.AFFILIATED.index ? campaigns : state.affiliatedCampaigns,
        pausedCampaigns: currentTabIndex == CampaignTabIndex.PAUSED.index ? campaigns : state.pausedCampaigns,
        currentPage: 1,
      ));
    } catch (e) {
      if (requestId != _currentRequestId) return;

      handleError(e, (message) {
        emit(state.copyWith(errorMessage: message));
        dev.log(message);
      });
    }
  }

  /// Refreshes campaign data after a campaign application is submitted
  /// This method ensures that:
  /// 1. The cache for campaign-related endpoints is properly invalidated
  /// 2. Fresh data is fetched from the server to update the campaign counts
  /// 3. The campaign list for the relevant tab is refreshed with the latest data
  /// 4. The UI is updated to reflect these changes
  Future<void> refreshAfterCampaignApplication() async {
    try {
      final apiService = Modular.get<ApiService>();

      await apiService.clearCacheForEndpoint('/v3/publishers/me/sites');

      final siteId = await commonCubit.sharedPreferencesService.getCurrentSiteId();
      if (siteId != null) {
        await apiService.clearCacheForEndpoint('/v3/publishers/me/sites/$siteId/campaigns/count-summary');

        await apiService.clearCacheForEndpoint('/v3/publishers/me/sites/$siteId/campaigns/available');
        await apiService.clearCacheForEndpoint('/v3/publishers/me/sites/$siteId/campaigns/waiting');
        await apiService.clearCacheForEndpoint('/v3/publishers/me/sites/$siteId/campaigns/affiliated');
        await apiService.clearCacheForEndpoint('/v3/publishers/me/sites/$siteId/campaigns/paused');
      }

      await refreshCampaignCountSummary();

      await refreshCurrentTabData();

      for (int i = 0; i < CampaignTabIndex.values.length; i++) {
        if (i != state.tabIndex) {
          _loadCampaigns(i, CampaignFilter());
        }
      }
    } catch (e) {
      handleError(e, (message) => dev.log("Error refreshing after campaign application: $message"));
    }
  }

  /// Clear cache for interested fields and campaign-related endpoints to ensure data consistency
  /// This ensures that campaign recommendations are updated immediately after interest changes
  Future<void> _clearInterestedFieldsRelatedCaches(int siteId) async {
    try {
      final apiService = Modular.get<ApiService>();

      await apiService.clearCacheForEndpoint('/v3/publishers/me/sites/$siteId/categories');

      await apiService.clearCacheForEndpoint('/v3/publishers/me/sites/$siteId/campaigns/featured-summary');
      await apiService.clearCacheForEndpoint('/v3/publishers/me/sites/$siteId/campaigns/top-summary');
      await apiService.clearCacheForEndpoint('/v3/publishers/me/sites/$siteId/campaigns/fastest-growing-summary');

      await apiService.clearCacheForEndpoint('/v3/publishers/me/sites/$siteId/campaigns/available');
      await apiService.clearCacheForEndpoint('/v3/publishers/me/sites/$siteId/campaigns/count-summary');
    } catch (e) {
      dev.log('Error clearing interested fields related caches: $e');
    }
  }
}
